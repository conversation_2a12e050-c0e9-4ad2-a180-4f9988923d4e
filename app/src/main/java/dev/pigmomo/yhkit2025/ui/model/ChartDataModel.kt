package dev.pigmomo.yhkit2025.ui.model

import java.util.Date

/**
 * 图表数据点
 * 表示时间序列图表中的一个数据点
 */
data class ChartDataPoint(
    /**
     * 时间戳
     */
    val timestamp: Date,
    
    /**
     * 数值
     */
    val value: Double,
    
    /**
     * 显示标签（可选）
     */
    val label: String = "",
    
    /**
     * 额外信息（用于详情显示）
     */
    val extraInfo: Map<String, String> = emptyMap()
)

/**
 * 图表数据类型枚举
 */
enum class ChartDataType(val displayName: String, val unit: String) {
    /**
     * 价格数据
     */
    PRICE("价格", "元"),
    
    /**
     * 库存数据
     */
    STOCK("库存", "件")
}

/**
 * 时间范围枚举
 */
enum class TimeRange(val displayName: String, val days: Int) {
    /**
     * 最近7天
     */
    LAST_7_DAYS("最近7天", 7),
    
    /**
     * 最近30天
     */
    LAST_30_DAYS("最近30天", 30),
    
    /**
     * 最近90天
     */
    LAST_90_DAYS("最近90天", 90),
    
    /**
     * 全部时间
     */
    ALL_TIME("全部时间", -1)
}

/**
 * 图表配置
 */
data class ChartConfig(
    /**
     * 数据类型
     */
    val dataType: ChartDataType = ChartDataType.PRICE,
    
    /**
     * 时间范围
     */
    val timeRange: TimeRange = TimeRange.LAST_7_DAYS,
    
    /**
     * 是否显示数据点
     */
    val showDataPoints: Boolean = true,
    
    /**
     * 是否显示网格线
     */
    val showGridLines: Boolean = true,
    
    /**
     * 线条颜色（十六进制）
     */
    val lineColor: String = "#2196F3",
    
    /**
     * 是否平滑曲线
     */
    val smoothLine: Boolean = true
)

/**
 * 图表数据集
 */
data class ChartDataSet(
    /**
     * 数据点列表
     */
    val dataPoints: List<ChartDataPoint>,
    
    /**
     * 图表配置
     */
    val config: ChartConfig,
    
    /**
     * 数据标题
     */
    val title: String = "",
    
    /**
     * 数据描述
     */
    val description: String = ""
) {
    /**
     * 获取最小值
     */
    val minValue: Double
        get() = dataPoints.minOfOrNull { it.value } ?: 0.0
    
    /**
     * 获取最大值
     */
    val maxValue: Double
        get() = dataPoints.maxOfOrNull { it.value } ?: 0.0
    
    /**
     * 获取平均值
     */
    val averageValue: Double
        get() = if (dataPoints.isNotEmpty()) {
            dataPoints.sumOf { it.value } / dataPoints.size
        } else 0.0
    
    /**
     * 获取数据点数量
     */
    val dataPointCount: Int
        get() = dataPoints.size
    
    /**
     * 是否有数据
     */
    val hasData: Boolean
        get() = dataPoints.isNotEmpty()
}

/**
 * 图表详情信息
 * 用于显示点击数据点时的详细信息
 */
data class ChartDetailInfo(
    /**
     * 数据点
     */
    val dataPoint: ChartDataPoint,
    
    /**
     * 格式化的时间
     */
    val formattedTime: String,
    
    /**
     * 格式化的数值
     */
    val formattedValue: String,
    
    /**
     * 额外的详情信息
     */
    val details: Map<String, String> = emptyMap()
)
