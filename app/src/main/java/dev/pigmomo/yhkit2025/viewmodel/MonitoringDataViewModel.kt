package dev.pigmomo.yhkit2025.viewmodel

import android.app.Application
import android.util.Log
import androidx.compose.runtime.State
import androidx.compose.runtime.mutableIntStateOf
import androidx.compose.runtime.mutableStateOf
import androidx.lifecycle.AndroidViewModel
import androidx.lifecycle.ViewModel
import androidx.lifecycle.ViewModelProvider
import androidx.lifecycle.viewModelScope
import dev.pigmomo.yhkit2025.data.model.productmonitor.MonitoringPlanEntity
import dev.pigmomo.yhkit2025.data.model.productmonitor.ProductMonitorEntity
import dev.pigmomo.yhkit2025.data.model.productmonitor.ProductChangeRecordEntity
import dev.pigmomo.yhkit2025.data.model.productmonitor.ProductChangeType
import dev.pigmomo.yhkit2025.ui.model.*
import dev.pigmomo.yhkit2025.data.repository.productmonitor.MonitoringPlanRepository
import dev.pigmomo.yhkit2025.data.repository.productmonitor.ProductMonitorRepository
import dev.pigmomo.yhkit2025.service.productmonitor.MonitoringServiceManager
import java.util.Calendar
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.map
import kotlinx.coroutines.launch
import java.text.SimpleDateFormat
import java.util.*

/**
 * 监控数据页面的ViewModel
 * 管理监控数据展示页面的状态和业务逻辑
 */
class MonitoringDataViewModel(
    application: Application,
    private val monitoringPlanRepository: MonitoringPlanRepository,
    private val productMonitorRepository: ProductMonitorRepository
) : AndroidViewModel(application) {

    // 监控计划列表状态
    private val _monitoringPlans = MutableStateFlow<List<MonitoringPlanEntity>>(emptyList())
    val monitoringPlans: StateFlow<List<MonitoringPlanEntity>> = _monitoringPlans.asStateFlow()

    // 监控商品列表状态
    private val _monitoredProducts = MutableStateFlow<List<ProductMonitorEntity>>(emptyList())
    val monitoredProducts: StateFlow<List<ProductMonitorEntity>> = _monitoredProducts.asStateFlow()

    // 变化记录列表状态
    private val _changeRecords = MutableStateFlow<List<ProductChangeRecordEntity>>(emptyList())
    val changeRecords: StateFlow<List<ProductChangeRecordEntity>> = _changeRecords.asStateFlow()

    // 选中商品状态
    private val _selectedProduct = MutableStateFlow<ProductMonitorEntity?>(null)
    val selectedProduct: StateFlow<ProductMonitorEntity?> = _selectedProduct.asStateFlow()

    // 选中商品的变化记录状态
    private val _selectedProductChangeRecords = MutableStateFlow<List<ProductChangeRecordEntity>>(emptyList())
    val selectedProductChangeRecords: StateFlow<List<ProductChangeRecordEntity>> = _selectedProductChangeRecords.asStateFlow()

    // 当前变化记录订阅的Job
    private var changeRecordsJob: kotlinx.coroutines.Job? = null

    // 加载状态
    private val _isLoading = mutableStateOf(true)
    val isLoading: State<Boolean> = _isLoading

    // 统计信息状态
    private val _statistics = MutableStateFlow<Map<String, Int>>(emptyMap())
    val statistics: StateFlow<Map<String, Int>> = _statistics.asStateFlow()

    // 错误信息状态
    private val _errorMessage = mutableStateOf<String?>(null)
    val errorMessage: State<String?> = _errorMessage

    // 日期格式化器
    val dateFormat = SimpleDateFormat("MM-dd HH:mm", Locale.getDefault())
    val fullDateFormat = SimpleDateFormat("yyyy-MM-dd HH:mm:ss", Locale.getDefault())

    init {
        initializeData()
    }

    /**
     * 初始化数据
     */
    private fun initializeData() {
        viewModelScope.launch {
            try {
                _isLoading.value = true
                _errorMessage.value = null

                // 启动数据监听
                startDataObservation()
                
                // 加载统计信息
                loadStatistics()

            } catch (e: Exception) {
                Log.e("MonitoringDataViewModel", "Failed to initialize data", e)
                _errorMessage.value = "初始化数据失败: ${e.message}"
            } finally {
                _isLoading.value = false
            }
        }
    }

    /**
     * 启动数据监听
     */
    private fun startDataObservation() {
        // 监听监控计划数据
        viewModelScope.launch {
            monitoringPlanRepository.getAllMonitoringPlans().collect { plans ->
                _monitoringPlans.value = plans
                Log.d("MonitoringDataViewModel", "Monitoring plans updated: ${plans.size}")
            }
        }

        // 监听监控商品数据
        viewModelScope.launch {
            productMonitorRepository.getAllProducts().collect { products ->
                _monitoredProducts.value = products
                Log.d("MonitoringDataViewModel", "Monitored products updated: ${products.size}")
            }
        }

        // 监听变化记录数据
        viewModelScope.launch {
            productMonitorRepository.getAllChangeRecords().collect { records ->
                _changeRecords.value = records.sortedByDescending { it.changeTime }
                Log.d("MonitoringDataViewModel", "Change records updated: ${records.size}")
            }
        }
    }

    /**
     * 加载统计信息
     */
    private suspend fun loadStatistics() {
        try {
            val stats = productMonitorRepository.getMonitoringStatistics()
            _statistics.value = stats
            Log.d("MonitoringDataViewModel", "Statistics loaded: $stats")
        } catch (e: Exception) {
            Log.e("MonitoringDataViewModel", "Failed to load statistics", e)
            _errorMessage.value = "加载统计信息失败: ${e.message}"
        }
    }

    /**
     * 刷新数据
     */
    fun refreshData() {
        viewModelScope.launch {
            try {
                _isLoading.value = true
                _errorMessage.value = null
                
                // 重新加载统计信息
                loadStatistics()
                
                Log.d("MonitoringDataViewModel", "Data refreshed successfully")
            } catch (e: Exception) {
                Log.e("MonitoringDataViewModel", "Failed to refresh data", e)
                _errorMessage.value = "刷新数据失败: ${e.message}"
            } finally {
                _isLoading.value = false
            }
        }
    }

    /**
     * 选择商品并加载其变化记录
     */
    fun selectProduct(product: ProductMonitorEntity?) {
        // 取消之前的订阅
        changeRecordsJob?.cancel()

        _selectedProduct.value = product
        if (product != null) {
            // 加载选中商品的变化记录
            changeRecordsJob = viewModelScope.launch {
                try {
                    productMonitorRepository.getProductChangeRecords(product.id,product.shopId).collect { records ->
                        _selectedProductChangeRecords.value = records.sortedByDescending { it.changeTime }
                        Log.d("MonitoringDataViewModel", "Product ${product.id} change records loaded: ${records.size}")
                        Log.d("MonitoringDataViewModel", "Change records details: ${records.map { "${it.changeType} at ${it.changeTime}" }}")
                    }
                } catch (e: Exception) {
                    Log.e("MonitoringDataViewModel", "Failed to load change records for product ${product.id}", e)
                    _selectedProductChangeRecords.value = emptyList()
                }
            }
        } else {
            _selectedProductChangeRecords.value = emptyList()
        }
    }

    /**
     * 获取指定商品的变化记录
     */
    fun getProductChangeRecords(productId: String, shopId: String): Flow<List<ProductChangeRecordEntity>> {
        return productMonitorRepository.getProductChangeRecords(productId, shopId)
    }

    /**
     * 删除商品监控记录（指定店铺）
     */
    fun deleteProduct(productId: String, shopId: String) {
        viewModelScope.launch {
            try {
                _isLoading.value = true
                val success = productMonitorRepository.deleteProductByShop(productId, shopId)
                if (success) {
                    Log.d("MonitoringDataViewModel", "Product $productId in shop $shopId deleted successfully")
                    // 刷新数据
                    refreshData()
                } else {
                    _errorMessage.value = "删除商品监控失败"
                    Log.e("MonitoringDataViewModel", "Failed to delete product $productId in shop $shopId")
                }
            } catch (e: Exception) {
                Log.e("MonitoringDataViewModel", "Error deleting product $productId in shop $shopId", e)
                _errorMessage.value = "删除商品监控失败: ${e.message}"
            } finally {
                _isLoading.value = false
            }
        }
    }

    /**
     * 清除错误信息
     */
    fun clearErrorMessage() {
        _errorMessage.value = null
    }

    /**
     * 生成图表数据集
     * @param productId 商品ID
     * @param shopId 店铺ID
     * @param config 图表配置
     * @return 图表数据集的Flow
     */
    fun generateChartDataSet(
        productId: String,
        shopId: String,
        config: ChartConfig
    ): Flow<ChartDataSet> {
        return productMonitorRepository.getProductChangeRecords(productId, shopId)
            .map { records ->
                val filteredRecords = filterRecordsByTimeRange(records, config.timeRange)
                val dataPoints = convertRecordsToDataPoints(filteredRecords, config.dataType)

                ChartDataSet(
                    dataPoints = dataPoints,
                    config = config,
                    title = "${config.dataType.displayName}变化趋势",
                    description = "最近${if (config.timeRange.days > 0) "${config.timeRange.days}天" else "全部"}的${config.dataType.displayName}变化"
                )
            }
    }

    /**
     * 根据时间范围过滤记录
     */
    private fun filterRecordsByTimeRange(
        records: List<ProductChangeRecordEntity>,
        timeRange: TimeRange
    ): List<ProductChangeRecordEntity> {
        if (timeRange.days <= 0) return records

        val calendar = Calendar.getInstance()
        calendar.add(Calendar.DAY_OF_YEAR, -timeRange.days)
        val cutoffDate = calendar.time

        return records.filter { it.changeTime.after(cutoffDate) }
    }

    /**
     * 将变化记录转换为图表数据点
     */
    private fun convertRecordsToDataPoints(
        records: List<ProductChangeRecordEntity>,
        dataType: ChartDataType
    ): List<ChartDataPoint> {
        val relevantRecords = when (dataType) {
            ChartDataType.PRICE -> records.filter { it.changeType == ProductChangeType.PRICE_CHANGE }
            ChartDataType.STOCK -> records.filter { it.changeType == ProductChangeType.STOCK_CHANGE }
        }

        return relevantRecords.mapNotNull { record ->
            val value = when (dataType) {
                ChartDataType.PRICE -> {
                    // 价格以分为单位存储，转换为元
                    record.newValue.toDoubleOrNull()?.let { it / 100.0 }
                }
                ChartDataType.STOCK -> {
                    // 库存以件为单位
                    record.newValue.toDoubleOrNull()?.let { it / 100.0 }
                }
            }

            value?.let {
                ChartDataPoint(
                    timestamp = record.changeTime,
                    value = it,
                    label = "${dataType.displayName}: ${String.format("%.2f", it)} ${dataType.unit}",
                    extraInfo = mapOf(
                        "changeType" to record.changeType.name,
                        "oldValue" to record.oldValue,
                        "newValue" to record.newValue,
                        "description" to record.changeDescription
                    )
                )
            }
        }.sortedBy { it.timestamp }
    }

    /**
     * ViewModel工厂
     */
    class Factory(
        private val application: Application,
        private val monitoringPlanRepository: MonitoringPlanRepository,
        private val productMonitorRepository: ProductMonitorRepository
    ) : ViewModelProvider.Factory {
        @Suppress("UNCHECKED_CAST")
        override fun <T : ViewModel> create(modelClass: Class<T>): T {
            if (modelClass.isAssignableFrom(MonitoringDataViewModel::class.java)) {
                return MonitoringDataViewModel(
                    application,
                    monitoringPlanRepository,
                    productMonitorRepository
                ) as T
            }
            throw IllegalArgumentException("Unknown ViewModel class")
        }
    }
}
